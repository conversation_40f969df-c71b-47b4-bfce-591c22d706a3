#!/usr/bin/env python3
"""
测试MCP服务器的权限配置
"""

import subprocess
import json
import time
import sys
import os

def test_mcp_permissions():
    """测试MCP服务器的路径权限"""
    print("=== 测试MCP服务器权限配置 ===")
    
    # 启动MCP服务器，使用配置文件
    try:
        print("启动MCP服务器（使用配置文件）...")
        process = subprocess.Popen(
            ["python3", "mcp_file_server.py", "--config", "mcp_allowed_paths.conf"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0
        )
        
        # 等待服务器启动
        time.sleep(1)
        
        # 检查进程是否还在运行
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"❌ MCP服务器启动失败")
            print(f"stdout: {stdout}")
            print(f"stderr: {stderr}")
            return False
        
        print("✅ MCP服务器启动成功")
        
        # 发送初始化请求
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2025-06-18",
                "capabilities": {},
                "clientInfo": {
                    "name": "TestClient",
                    "version": "1.0.0"
                }
            }
        }
        
        print("发送初始化请求...")
        process.stdin.write(json.dumps(init_request) + "\n")
        process.stdin.flush()
        
        # 读取响应
        response_line = process.stdout.readline()
        if response_line:
            try:
                response = json.loads(response_line.strip())
                print(f"✅ 收到初始化响应")
                
                if "error" in response:
                    print(f"❌ 初始化错误: {response['error']}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ 响应解析失败: {e}")
                return False
        
        # 发送初始化完成通知
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }
        
        process.stdin.write(json.dumps(initialized_notification) + "\n")
        process.stdin.flush()
        
        # 测试访问不同路径
        test_paths = [
            "/home/<USER>",
            "/home/<USER>/Nuctech_Services",
            "/tmp",
            "/home/<USER>/Desktop",
            "/root"  # 这个应该被拒绝
        ]
        
        for test_path in test_paths:
            print(f"\n测试访问路径: {test_path}")
            
            # 发送list_directory请求
            list_request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/call",
                "params": {
                    "name": "list_directory",
                    "arguments": {
                        "path": test_path
                    }
                }
            }
            
            process.stdin.write(json.dumps(list_request) + "\n")
            process.stdin.flush()
            
            # 读取响应
            response_line = process.stdout.readline()
            if response_line:
                try:
                    response = json.loads(response_line.strip())
                    
                    if "result" in response and not response["result"].get("isError", False):
                        print(f"  ✅ 访问成功")
                    elif "result" in response and response["result"].get("isError", False):
                        error_msg = response["result"].get("content", "Unknown error")
                        if "Access denied" in error_msg:
                            print(f"  ❌ 访问被拒绝（预期行为）")
                        else:
                            print(f"  ❌ 其他错误: {error_msg}")
                    else:
                        print(f"  ❌ 响应格式错误: {response}")
                        
                except json.JSONDecodeError as e:
                    print(f"  ❌ 响应解析失败: {e}")
            else:
                print(f"  ❌ 没有收到响应")
        
        # 清理
        process.terminate()
        process.wait(timeout=5)
        print("\n✅ 权限测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        if 'process' in locals():
            process.terminate()
        return False

if __name__ == "__main__":
    success = test_mcp_permissions()
    sys.exit(0 if success else 1)
