#!/usr/bin/env python3
"""
测试AppImage中的MCP服务器功能
"""

import subprocess
import json
import time
import sys
import os

def test_mcp_server():
    """测试MCP服务器启动和基本功能"""
    print("=== 测试AppImage中的MCP服务器 ===")
    
    # 测试路径
    mcp_script = "./squashfs-root/usr/bin/mcp_file_server.py"
    if not os.path.exists(mcp_script):
        print(f"❌ MCP脚本不存在: {mcp_script}")
        return False
    
    print(f"✅ 找到MCP脚本: {mcp_script}")
    
    # 启动MCP服务器
    try:
        print("启动MCP服务器...")
        process = subprocess.Popen(
            ["python3", mcp_script, "--allowed-paths", os.getcwd()],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0
        )
        
        # 等待服务器启动
        time.sleep(1)
        
        # 检查进程是否还在运行
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"❌ MCP服务器启动失败")
            print(f"stdout: {stdout}")
            print(f"stderr: {stderr}")
            return False
        
        print("✅ MCP服务器启动成功")
        
        # 发送初始化请求
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2025-06-18",
                "capabilities": {},
                "clientInfo": {
                    "name": "TestClient",
                    "version": "1.0.0"
                }
            }
        }
        
        print("发送初始化请求...")
        process.stdin.write(json.dumps(init_request) + "\n")
        process.stdin.flush()
        
        # 读取响应
        response_line = process.stdout.readline()
        if response_line:
            try:
                response = json.loads(response_line.strip())
                print(f"✅ 收到初始化响应: {response.get('id')}")
                
                if "error" in response:
                    print(f"❌ 初始化错误: {response['error']}")
                    return False
                else:
                    print("✅ 初始化成功")
                    
            except json.JSONDecodeError as e:
                print(f"❌ 响应解析失败: {e}")
                print(f"原始响应: {response_line}")
                return False
        else:
            print("❌ 没有收到响应")
            return False
        
        # 发送初始化完成通知
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }
        
        print("发送初始化完成通知...")
        process.stdin.write(json.dumps(initialized_notification) + "\n")
        process.stdin.flush()
        
        # 获取工具列表
        list_tools_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list"
        }
        
        print("获取工具列表...")
        process.stdin.write(json.dumps(list_tools_request) + "\n")
        process.stdin.flush()
        
        # 读取工具列表响应
        tools_response_line = process.stdout.readline()
        if tools_response_line:
            try:
                tools_response = json.loads(tools_response_line.strip())
                print(f"✅ 收到工具列表响应")
                
                if "result" in tools_response and "tools" in tools_response["result"]:
                    tools = tools_response["result"]["tools"]
                    print(f"✅ 可用工具数量: {len(tools)}")
                    for tool in tools:
                        print(f"  - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}")
                else:
                    print("❌ 工具列表格式错误")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ 工具列表响应解析失败: {e}")
                return False
        else:
            print("❌ 没有收到工具列表响应")
            return False
        
        # 清理
        process.terminate()
        process.wait(timeout=5)
        print("✅ MCP服务器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        if 'process' in locals():
            process.terminate()
        return False

if __name__ == "__main__":
    success = test_mcp_server()
    sys.exit(0 if success else 1)
