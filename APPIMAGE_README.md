# Mini Tool AppImage 使用说明

## 概述

这个AppImage包含了完整的Mini Tool应用程序，包括：
- C++编写的GUI主程序
- Python编写的MCP文件服务器
- 所有必要的依赖库

## 构建AppImage

运行构建脚本：
```bash
./build-appimage-simple.sh
```

构建完成后会生成 `MiniTool-1.0.0-x86_64.AppImage` 文件。

## 使用方法

### 1. 运行主程序

```bash
# 给AppImage添加执行权限
chmod +x MiniTool-1.0.0-x86_64.AppImage

# 运行GUI程序
./MiniTool-1.0.0-x86_64.AppImage
```

### 2. 使用Python功能

#### 方法一：提取AppImage内容
```bash
# 提取AppImage内容到当前目录
./MiniTool-1.0.0-x86_64.AppImage --appimage-extract

# 运行MCP文件服务器
./squashfs-root/usr/bin/mcp-server --allowed-paths /path/to/your/directory

# 或者直接运行Python文件
python3 ./squashfs-root/usr/share/mini_tool/mcp_file_server.py --allowed-paths /path/to/your/directory
```

#### 方法二：使用AppImage挂载
```bash
# 挂载AppImage（需要FUSE支持）
./MiniTool-1.0.0-x86_64.AppImage --appimage-mount &
MOUNT_POINT=$(ps aux | grep appimage-mount | grep -v grep | awk '{print $NF}')

# 运行Python脚本
python3 "$MOUNT_POINT/usr/share/mini_tool/mcp_file_server.py" --allowed-paths /path/to/your/directory
```

### 3. MCP文件服务器功能

MCP文件服务器提供以下功能：
- 文件读取和写入
- 目录列表
- 文件压缩（ZIP格式）
- 安全的路径验证

使用示例：
```bash
# 启动服务器，允许访问指定目录
python3 mcp_file_server.py --allowed-paths /home/<USER>/documents /home/<USER>/projects

# 服务器会监听标准输入，接收JSON-RPC格式的请求
```

## 包含的文件

AppImage中包含以下主要文件：

### 可执行文件
- `/usr/bin/mini_tool` - 主GUI程序
- `/usr/bin/mcp-server` - MCP服务器启动脚本
- `/usr/bin/single_pkg_build.sh` - 编译脚本（如果存在）

### Python文件
- `/usr/share/mini_tool/mcp_file_server.py` - MCP文件服务器
- `/usr/share/mini_tool/test_mcp.py` - 测试脚本（如果存在）

### 依赖库
- `/usr/lib/` - 所有必要的动态链接库

## 系统要求

- Linux x86_64系统
- Python 3.x（用于Python功能）
- FUSE（用于AppImage挂载，可选）

## 测试功能

### 测试MCP服务器
```bash
# 提取AppImage
./MiniTool-1.0.0-x86_64.AppImage --appimage-extract

# 测试MCP服务器功能
python3 test_appimage_mcp.py
```

## 故障排除

### 1. AppImage无法运行
```bash
# 检查是否有执行权限
ls -la MiniTool-1.0.0-x86_64.AppImage

# 检查FUSE支持
fusermount --version
```

### 2. Python功能无法使用
```bash
# 检查Python版本
python3 --version

# 检查必要模块
python3 -c "import asyncio, json, sys, os, zipfile"
```

### 3. MCP服务器初始化失败
如果遇到 "Broken pipe" 错误：
```bash
# 检查Python包装器脚本
python3 ./squashfs-root/usr/bin/mcp_file_server.py --help

# 检查实际的Python文件
ls -la ./squashfs-root/usr/share/mini_tool/
ls -la ./squashfs-root/usr/bin/mcp_file_server.py
```

### 4. 权限问题
```bash
# 确保AppImage有执行权限
chmod +x MiniTool-1.0.0-x86_64.AppImage

# 确保提取的文件有正确权限
chmod +x squashfs-root/usr/bin/*
```

## 开发说明

如果需要修改Python代码：
1. 修改源码中的 `mcp_file_server.py`
2. 重新运行构建脚本
3. 新的AppImage会包含更新的Python代码

## 技术细节

- AppImage格式：Type 2
- 压缩格式：SquashFS
- 运行时：官方runtime-x86_64
- Python路径：通过PYTHONPATH环境变量设置
- 库路径：通过LD_LIBRARY_PATH环境变量设置

### Python文件打包策略

为了确保C++程序能正确找到MCP服务器，Python文件被打包到两个位置：

1. **标准位置**: `/usr/share/mini_tool/mcp_file_server.py` - 实际的Python文件
2. **兼容位置**: `/usr/bin/mcp_file_server.py` - Python包装器脚本

包装器脚本会自动查找实际的Python文件并执行，解决了路径查找问题。

### 修复的问题

- ✅ 修复了 "Broken pipe" 错误
- ✅ 修复了路径查找问题
- ✅ 添加了Python环境检查
- ✅ 创建了智能包装器脚本
- ✅ 支持多种启动方式
