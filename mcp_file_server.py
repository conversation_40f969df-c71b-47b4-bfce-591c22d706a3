#!/usr/bin/env python3
"""
简单的MCP文件操作服务器
提供基本的文件系统操作工具
"""

import asyncio
import json
import sys
import os
import zipfile
from typing import Any, Dict, List

class MCPFileServer:
    def __init__(self, allowed_paths: List[str] = None):
        self.allowed_paths = allowed_paths or [os.getcwd()]
        self.request_id = 0
        
    def is_path_allowed(self, path: str) -> bool:
        """检查路径是否在允许的范围内"""
        try:
            # 解析路径，防止路径遍历攻击
            abs_path = os.path.abspath(os.path.normpath(path))

            # 检查是否包含危险的路径组件
            dangerous_patterns = ['..', '~', '$']
            for pattern in dangerous_patterns:
                if pattern in path:
                    return False

            # 检查是否在允许的路径范围内
            for allowed in self.allowed_paths:
                allowed_abs = os.path.abspath(os.path.normpath(allowed))
                if abs_path.startswith(allowed_abs + os.sep) or abs_path == allowed_abs:
                    return True
            return False
        except Exception as e:
            print(f"Path validation error: {e}", file=sys.stderr)
            return False
    
    def create_response(self, request_id: Any, result: Any = None, error: Dict = None) -> Dict:
        """创建JSON-RPC响应"""
        response = {
            "jsonrpc": "2.0",
            "id": request_id
        }
        
        if error:
            response["error"] = error
        else:
            response["result"] = result
            
        return response
    
    def create_error(self, code: int, message: str, data: Any = None) -> Dict:
        """创建错误对象"""
        error = {
            "code": code,
            "message": message
        }
        if data:
            error["data"] = data
        return error
    
    async def handle_initialize(self, params: Dict) -> Dict:  # pylint: disable=unused-argument
        """处理初始化请求"""
        return {
            "protocolVersion": "2025-06-18",
            "capabilities": {
                "tools": {}
            },
            "serverInfo": {
                "name": "file-operations-server",
                "version": "1.0.0"
            }
        }
    
    async def handle_tools_list(self, params: Dict) -> Dict:  # pylint: disable=unused-argument
        """返回可用工具列表"""
        tools = [
            {
                "name": "read_file",
                "description": "读取文件内容",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "要读取的文件路径"
                        }
                    },
                    "required": ["path"]
                }
            },
            {
                "name": "write_file",
                "description": "写入文件内容",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "要写入的文件路径"
                        },
                        "content": {
                            "type": "string",
                            "description": "要写入的内容"
                        }
                    },
                    "required": ["path", "content"]
                }
            },
            {
                "name": "list_directory",
                "description": "列出目录内容",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "要列出的目录路径"
                        }
                    },
                    "required": ["path"]
                }
            },
            {
                "name": "delete_file",
                "description": "删除文件",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "要删除的文件路径"
                        }
                    },
                    "required": ["path"]
                }
            },
            {
                "name": "create_directory",
                "description": "创建目录",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "要创建的目录路径"
                        }
                    },
                    "required": ["path"]
                }
            },
            {
                "name": "compress_directory",
                "description": "压缩目录为ZIP文件",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "directory_path": {
                            "type": "string",
                            "description": "要压缩的目录路径"
                        },
                        "output_path": {
                            "type": "string",
                            "description": "输出ZIP文件路径（可选，默认为目录名.zip）"
                        }
                    },
                    "required": ["directory_path"]
                }
            }
        ]
        
        return {"tools": tools}
    
    async def handle_tool_call(self, params: Dict) -> Dict:
        """处理工具调用"""
        tool_name = params.get("name")
        arguments = params.get("arguments", {})
        
        try:
            if tool_name == "read_file":
                return await self.read_file(arguments)
            elif tool_name == "write_file":
                return await self.write_file(arguments)
            elif tool_name == "list_directory":
                return await self.list_directory(arguments)
            elif tool_name == "delete_file":
                return await self.delete_file(arguments)
            elif tool_name == "create_directory":
                return await self.create_directory(arguments)
            elif tool_name == "compress_directory":
                return await self.compress_directory(arguments)
            else:
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Unknown tool: {tool_name}"}]
                }
        except Exception as e:
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Error executing tool: {str(e)}"}]
            }
    
    async def read_file(self, args: Dict) -> Dict:
        """读取文件"""
        path = args.get("path")
        if not path:
            raise ValueError("Path is required")
        
        if not self.is_path_allowed(path):
            raise PermissionError(f"Access denied to path: {path}")
        
        if not os.path.exists(path):
            raise FileNotFoundError(f"File not found: {path}")
        
        if not os.path.isfile(path):
            raise ValueError(f"Path is not a file: {path}")
        
        try:
            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return {
                "content": [{"type": "text", "text": content}]
            }
        except UnicodeDecodeError:
            # 尝试以二进制模式读取
            with open(path, 'rb') as f:
                content = f.read()
            
            return {
                "content": [{"type": "text", "text": f"Binary file, size: {len(content)} bytes"}]
            }
    
    async def write_file(self, args: Dict) -> Dict:
        """写入文件"""
        path = args.get("path")
        content = args.get("content")

        if not path:
            raise ValueError("Path is required")
        if content is None:
            raise ValueError("Content is required")

        if not self.is_path_allowed(path):
            raise PermissionError(f"Access denied to path: {path}")

        # 确保目录存在
        dir_path = os.path.dirname(path)
        if dir_path:  # 只有当目录路径不为空时才创建
            os.makedirs(dir_path, exist_ok=True)
        with open(path, 'w', encoding='utf-8') as f:
            f.write(content)

        return {
            "content": [{"type": "text", "text": f"Successfully wrote {len(content)} characters to {path}"}]
        }
    
    async def list_directory(self, args: Dict) -> Dict:
        """列出目录内容"""
        path = args.get("path", ".")
        
        if not self.is_path_allowed(path):
            raise PermissionError(f"Access denied to path: {path}")
        
        if not os.path.exists(path):
            raise FileNotFoundError(f"Directory not found: {path}")
        
        if not os.path.isdir(path):
            raise ValueError(f"Path is not a directory: {path}")
        
        items = []
        for item in sorted(os.listdir(path)):
            item_path = os.path.join(path, item)
            if os.path.isdir(item_path):
                items.append(f"📁 {item}/")
            else:
                size = os.path.getsize(item_path)
                items.append(f"📄 {item} ({size} bytes)")
        
        content = "\n".join(items) if items else "Directory is empty"
        
        return {
            "content": [{"type": "text", "text": content}]
        }
    
    async def delete_file(self, args: Dict) -> Dict:
        """删除文件"""
        path = args.get("path")
        
        if not path:
            raise ValueError("Path is required")
        
        if not self.is_path_allowed(path):
            raise PermissionError(f"Access denied to path: {path}")
        
        if not os.path.exists(path):
            raise FileNotFoundError(f"File not found: {path}")
        
        if os.path.isdir(path):
            os.rmdir(path)
            return {
                "content": [{"type": "text", "text": f"Successfully deleted directory: {path}"}]
            }
        else:
            os.remove(path)
            return {
                "content": [{"type": "text", "text": f"Successfully deleted file: {path}"}]
            }
    
    async def create_directory(self, args: Dict) -> Dict:
        """创建目录"""
        path = args.get("path")
        
        if not path:
            raise ValueError("Path is required")
        
        if not self.is_path_allowed(path):
            raise PermissionError(f"Access denied to path: {path}")
        
        os.makedirs(path, exist_ok=True)
        
        return {
            "content": [{"type": "text", "text": f"Successfully created directory: {path}"}]
        }

    async def compress_directory(self, args: Dict) -> Dict:
        """压缩目录为ZIP文件"""
        directory_path = args.get("directory_path")
        output_path = args.get("output_path")

        if not directory_path:
            raise ValueError("Directory path is required")

        if not self.is_path_allowed(directory_path):
            raise PermissionError(f"Access denied to path: {directory_path}")

        if not os.path.exists(directory_path):
            raise FileNotFoundError(f"Directory not found: {directory_path}")

        if not os.path.isdir(directory_path):
            raise ValueError(f"Path is not a directory: {directory_path}")

        # 如果没有指定输出路径，使用目录名.zip
        if not output_path:
            output_path = f"{directory_path.rstrip(os.sep)}.zip"

        if not self.is_path_allowed(output_path):
            raise PermissionError(f"Access denied to output path: {output_path}")

        # 创建ZIP文件
        with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(directory_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    # 计算相对路径，避免包含完整路径
                    arcname = os.path.relpath(file_path, directory_path)
                    zipf.write(file_path, arcname)

        # 获取压缩文件大小
        zip_size = os.path.getsize(output_path)

        return {
            "content": [{"type": "text", "text": f"Successfully compressed directory '{directory_path}' to '{output_path}' (size: {zip_size} bytes)"}]
        }

    async def handle_request(self, request: Dict) -> Dict:
        """处理JSON-RPC请求"""
        method = request.get("method")
        params = request.get("params", {})
        request_id = request.get("id")

        try:
            if method == "initialize":
                result = await self.handle_initialize(params)
                return self.create_response(request_id, result)
            elif method == "tools/list":
                result = await self.handle_tools_list(params)
                return self.create_response(request_id, result)
            elif method == "tools/call":
                result = await self.handle_tool_call(params)
                return self.create_response(request_id, result)
            else:
                error = self.create_error(-32601, f"Method not found: {method}")
                return self.create_response(request_id, error=error)
        except Exception as e:
            error = self.create_error(-32603, f"Internal error: {str(e)}")
            return self.create_response(request_id, error=error)

    async def run(self):
        """运行MCP服务器"""
        while True:
            try:
                # 从stdin读取请求
                line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
                if not line:
                    break

                line = line.strip()
                if not line:
                    continue

                # 解析JSON请求
                try:
                    request = json.loads(line)
                except json.JSONDecodeError as e:
                    error_response = {
                        "jsonrpc": "2.0",
                        "id": None,
                        "error": self.create_error(-32700, f"Parse error: {str(e)}")
                    }
                    print(json.dumps(error_response), flush=True)
                    continue

                # 处理请求
                if "method" in request:
                    method = request.get("method")

                    # 处理通知（不需要响应）
                    if method == "notifications/initialized":
                        # 初始化完成通知，不需要响应
                        pass
                    else:
                        # 这是一个请求，需要响应
                        response = await self.handle_request(request)
                        print(json.dumps(response), flush=True)

            except KeyboardInterrupt:
                break
            except Exception as e:
                # 记录错误但继续运行
                error_response = {
                    "jsonrpc": "2.0",
                    "id": None,
                    "error": self.create_error(-32603, f"Server error: {str(e)}")
                }
                print(json.dumps(error_response), flush=True)

def load_allowed_paths_from_config(config_file="mcp_allowed_paths.conf"):
    """从配置文件加载允许的路径"""
    allowed_paths = []

    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    # 跳过空行和注释
                    if line and not line.startswith('#'):
                        # 处理相对路径
                        if line.startswith('.'):
                            line = os.path.abspath(line)
                        allowed_paths.append(line)
        except Exception as e:
            print(f"Warning: Failed to load config file {config_file}: {e}", file=sys.stderr)

    return allowed_paths

async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="MCP File Operations Server")
    parser.add_argument("--allowed-paths", nargs="*",
                       help="Allowed file system paths (default: current directory)")
    parser.add_argument("--config", default="mcp_allowed_paths.conf",
                       help="Configuration file for allowed paths")

    args = parser.parse_args()

    # 优先使用命令行参数，然后是配置文件，最后是默认值
    if args.allowed_paths:
        allowed_paths = args.allowed_paths
    else:
        allowed_paths = load_allowed_paths_from_config(args.config)
        if not allowed_paths:
            allowed_paths = [os.getcwd()]

    print(f"MCP Server allowed paths: {allowed_paths}", file=sys.stderr)
    server = MCPFileServer(allowed_paths)
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())
